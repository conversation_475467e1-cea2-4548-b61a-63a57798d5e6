// formulaBuilder.js

/**
 * FormulaBuilder class generates Google Sheets formulas for budget calculations
 */
class FormulaBuilder {
  constructor() {
    this.currentYear = new Date().getFullYear();
  }

  /**
   * Generate enhanced monthly summary formulas for income, expenses, and net cash flow
   * @param {string} sheetName - Name of the monthly sheet
   * @param {string} dataRange - Range containing transaction data (e.g., "A2:D500")
   * @returns {Object} Object containing comprehensive summary formulas
   */
  getMonthlyTotalFormulas(sheetName, dataRange = "A2:D500") {
    return {
      totalIncome: `=SUMIF(E2:E500,"Income",D2:D500)`,
      totalExpenses: `=SUMIF(E2:E500,"Expense",D2:D500)`,
      netCashFlow: `=B503-B504`, // References the Total Income and Total Expenses cells
      budgetedIncome: `=SUMIF(Categories!B:B,"Income",Categories!C:C)`,
      budgetedExpenses: `=SUMIF(Categories!B:B,"Expense",Categories!C:C)`,
      budgetVariance: `=B503-B504-(SUMIF(Categories!B:B,"Income",Categories!C:C)-SUMIF(Categories!B:B,"Expense",Categories!C:C))`
    };
  }

  /**
   * Generate enhanced recurring transaction auto-population formulas
   * @param {string} monthName - Month name (e.g., "January")
   * @param {number} monthNumber - Month number (1-12)
   * @returns {Object} Object containing simplified recurring transaction formulas
   */
  getRecurringTransactionFormulas(monthName, monthNumber) {
    return {
      // Simple approach: populate first few rows with recurring transactions for this month
      recurringRow1: {
        date: `=IF(COUNTIFS(Recurring!G:G,">="&DATE(2025,${monthNumber},1),Recurring!G:G,"<"&DATE(2025,${monthNumber}+1,1),Recurring!H:H,TRUE)>=1,INDEX(Recurring!G:G,MATCH(TRUE,(MONTH(Recurring!G:G)=${monthNumber})*(Recurring!H:H=TRUE),0)),"")`,
        description: `=IF(COUNTIFS(Recurring!G:G,">="&DATE(2025,${monthNumber},1),Recurring!G:G,"<"&DATE(2025,${monthNumber}+1,1),Recurring!H:H,TRUE)>=1,INDEX(Recurring!B:B,MATCH(TRUE,(MONTH(Recurring!G:G)=${monthNumber})*(Recurring!H:H=TRUE),0)),"")`,
        category: `=IF(COUNTIFS(Recurring!G:G,">="&DATE(2025,${monthNumber},1),Recurring!G:G,"<"&DATE(2025,${monthNumber}+1,1),Recurring!H:H,TRUE)>=1,INDEX(Recurring!C:C,MATCH(TRUE,(MONTH(Recurring!G:G)=${monthNumber})*(Recurring!H:H=TRUE),0)),"")`,
        amount: `=IF(COUNTIFS(Recurring!G:G,">="&DATE(2025,${monthNumber},1),Recurring!G:G,"<"&DATE(2025,${monthNumber}+1,1),Recurring!H:H,TRUE)>=1,INDEX(Recurring!D:D,MATCH(TRUE,(MONTH(Recurring!G:G)=${monthNumber})*(Recurring!H:H=TRUE),0)),"")`,
        type: `=IF(COUNTIFS(Recurring!G:G,">="&DATE(2025,${monthNumber},1),Recurring!G:G,"<"&DATE(2025,${monthNumber}+1,1),Recurring!H:H,TRUE)>=1,INDEX(Recurring!E:E,MATCH(TRUE,(MONTH(Recurring!G:G)=${monthNumber})*(Recurring!H:H=TRUE),0)),"")`,
        status: `=IF(COUNTIFS(Recurring!G:G,">="&DATE(2025,${monthNumber},1),Recurring!G:G,"<"&DATE(2025,${monthNumber}+1,1),Recurring!H:H,TRUE)>=1,"Pending","")`,
        account: `=IF(COUNTIFS(Recurring!G:G,">="&DATE(2025,${monthNumber},1),Recurring!G:G,"<"&DATE(2025,${monthNumber}+1,1),Recurring!H:H,TRUE)>=1,INDEX(Recurring!I:I,MATCH(TRUE,(MONTH(Recurring!G:G)=${monthNumber})*(Recurring!H:H=TRUE),0)),"")`,
        notes: `=IF(COUNTIFS(Recurring!G:G,">="&DATE(2025,${monthNumber},1),Recurring!G:G,"<"&DATE(2025,${monthNumber}+1,1),Recurring!H:H,TRUE)>=1,"Auto-populated from recurring","")`,
        budget: `=IF(C2<>"",IFERROR(INDEX(Categories!C:C,MATCH(C2,Categories!A:A,0)),0),"")`,
        variance: `=IF(AND(C2<>"",D2<>""),IFERROR(INDEX(Categories!C:C,MATCH(C2,Categories!A:A,0))-D2,0),"")`
      }
    };
  }

  /**
   * Get budget variance formulas by category for summary section
   * @param {string} sheetName - Name of the monthly sheet
   * @returns {Object} Object containing budget comparison formulas
   */
  getBudgetVarianceFormulas(sheetName) {
    return {
      // Calculate actual spending by category
      categoryActual: `=SUMIF(C2:C500,A1,D2:D500)`,
      // Get budgeted amount for category from Categories sheet
      categoryBudget: `=IFERROR(INDEX(Categories!C:C,MATCH(A1,Categories!A:A,0)),0)`,
      // Calculate variance (positive = under budget, negative = over budget)
      categoryVariance: `=IFERROR(INDEX(Categories!C:C,MATCH(A1,Categories!A:A,0))-SUMIF(C$2:C$500,A1,D$2:D$500),0)`,
      // Percentage of budget used
      budgetPercentage: `=IFERROR(SUMIF(C$2:C$500,A1,D$2:D$500)/INDEX(Categories!C:C,MATCH(A1,Categories!A:A,0)),0)`
    };
  }

  /**
   * Generate budget vs actual comparison formulas
   * @param {string} category - Budget category
   * @param {string} monthlyDataRange - Range of monthly transaction data
   * @returns {Object} Budget comparison formulas
   */
  getBudgetComparisonFormulas(category, monthlyDataRange = "A2:G1000") {
    return {
      budgetAmount: `=INDEX(Settings!C:C,MATCH("${category}",Settings!B:B,0))`,
      actualAmount: `=SUMIFS(${monthlyDataRange.split(':')[0].replace(/[A-Z]/g, 'D')}:${monthlyDataRange.split(':')[1].replace(/[A-Z]/g, 'D')},${monthlyDataRange.split(':')[0].replace(/[A-Z]/g, 'C')}:${monthlyDataRange.split(':')[1].replace(/[A-Z]/g, 'C')},"${category}",${monthlyDataRange.split(':')[0].replace(/[A-Z]/g, 'E')}:${monthlyDataRange.split(':')[1].replace(/[A-Z]/g, 'E')},"Expense")`,
      variance: `=INDEX(Settings!C:C,MATCH("${category}",Settings!B:B,0))-SUMIFS(${monthlyDataRange.split(':')[0].replace(/[A-Z]/g, 'D')}:${monthlyDataRange.split(':')[1].replace(/[A-Z]/g, 'D')},${monthlyDataRange.split(':')[0].replace(/[A-Z]/g, 'C')}:${monthlyDataRange.split(':')[1].replace(/[A-Z]/g, 'C')},"${category}",${monthlyDataRange.split(':')[0].replace(/[A-Z]/g, 'E')}:${monthlyDataRange.split(':')[1].replace(/[A-Z]/g, 'E')},"Expense")`,
      percentUsed: `=SUMIFS(${monthlyDataRange.split(':')[0].replace(/[A-Z]/g, 'D')}:${monthlyDataRange.split(':')[1].replace(/[A-Z]/g, 'D')},${monthlyDataRange.split(':')[0].replace(/[A-Z]/g, 'C')}:${monthlyDataRange.split(':')[1].replace(/[A-Z]/g, 'C')},"${category}",${monthlyDataRange.split(':')[0].replace(/[A-Z]/g, 'E')}:${monthlyDataRange.split(':')[1].replace(/[A-Z]/g, 'E')},"Expense")/INDEX(Settings!C:C,MATCH("${category}",Settings!B:B,0))`
    };
  }

  /**
   * Generate annual dashboard summary formulas
   * @returns {Object} Annual dashboard formulas
   */
  getAnnualDashboardFormulas() {
    const months = this.getMonthNames();
    
    return {
      ytdIncome: `=${months.map(month => `${month}!D2`).join('+')}`,
      ytdExpenses: `=${months.map(month => `${month}!D3`).join('+')}`,
      ytdNet: `=${months.map(month => `${month}!D4`).join('+')}`,
      monthlyIncomeRange: `={${months.map(month => `${month}!D2`).join(';')}}`,
      monthlyExpenseRange: `={${months.map(month => `${month}!D3`).join(';')}}`,
      monthlyNetRange: `={${months.map(month => `${month}!D4`).join(';')}}`
    };
  }

  /**
   * Generate debt tracking formulas
   * @param {number} row - Row number for calculations
   * @returns {Object} Debt calculation formulas
   */
  getDebtTrackingFormulas(row) {
    return {
      monthlyPayment: `=D${row}`, // Minimum payment from user input
      remainingPayments: `=IF(D${row}=0,0,ROUNDUP(C${row}/D${row},0))`,
      payoffDate: `=IF(D${row}=0,"",EDATE(TODAY(),ROUNDUP(C${row}/D${row},0)))`,
      totalInterest: `=IF(D${row}=0,0,(ROUNDUP(C${row}/D${row},0)*D${row})-C${row})`,
      annualTotal: `=D${row}*12`
    };
  }

  /**
   * Generate savings tracking formulas
   * @param {number} row - Row number for calculations
   * @returns {Object} Savings calculation formulas
   */
  getSavingsTrackingFormulas(row) {
    return {
      progressPercent: `=IF(B${row}=0,0,C${row}/B${row})`,
      remainingAmount: `=B${row}-C${row}`,
      monthsToGoal: `=IF(D${row}=0,0,ROUNDUP((B${row}-C${row})/D${row},0))`,
      projectedDate: `=IF(D${row}=0,"",EDATE(TODAY(),ROUNDUP((B${row}-C${row})/D${row},0)))`,
      onTrack: `=IF(E${row}="","",IF(EDATE(TODAY(),ROUNDUP((B${row}-C${row})/D${row},0))<=E${row},"On Track","Behind"))`
    };
  }

  /**
   * Generate subscription tracking formulas
   * @param {number} row - Row number for calculations
   * @returns {Object} Subscription calculation formulas
   */
  getSubscriptionFormulas(row) {
    return {
      nextDueDate: `=IF(C${row}="Monthly",EDATE(D${row},1),IF(C${row}="Quarterly",EDATE(D${row},3),IF(C${row}="Annually",EDATE(D${row},12),D${row})))`,
      annualTotal: `=IF(C${row}="Monthly",B${row}*12,IF(C${row}="Quarterly",B${row}*4,IF(C${row}="Annually",B${row},B${row}*12)))`,
      daysUntilDue: `=IF(C${row}="Monthly",EDATE(D${row},1)-TODAY(),IF(C${row}="Quarterly",EDATE(D${row},3)-TODAY(),IF(C${row}="Annually",EDATE(D${row},12)-TODAY(),D${row}-TODAY())))`
    };
  }

  /**
   * Generate data validation formulas for dropdowns
   * @returns {Object} Data validation ranges
   */
  getDataValidationRanges() {
    return {
      categories: "Categories!A:A",
      transactionTypes: "Settings!E2:E4", // Income, Expense, Transfer
      transactionStatus: "Settings!F2:F4", // Pending, Cleared, Reconciled
      billingFrequency: "Settings!G2:G5" // Monthly, Quarterly, Annually, One-time
    };
  }

  /**
   * Helper method to get month number from month name
   * @param {string} monthName - Month name (e.g., "January")
   * @returns {number} Month number (1-12)
   */
  getMonthNumber(monthName) {
    const months = {
      'January': 1, 'February': 2, 'March': 3, 'April': 4,
      'May': 5, 'June': 6, 'July': 7, 'August': 8,
      'September': 9, 'October': 10, 'November': 11, 'December': 12
    };
    return months[monthName] || 1;
  }

  /**
   * Helper method to get all month names
   * @returns {Array<string>} Array of month names
   */
  getMonthNames() {
    return ['January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'];
  }

  /**
   * Generate conditional formatting rules for amounts
   * @returns {Object} Conditional formatting rules
   */
  getConditionalFormattingRules() {
    return {
      negativeAmounts: {
        condition: {
          type: 'NUMBER_LESS',
          values: [{ userEnteredValue: '0' }]
        },
        format: {
          textFormat: { foregroundColor: { red: 1, green: 0, blue: 0 } }
        }
      },
      positiveAmounts: {
        condition: {
          type: 'NUMBER_GREATER',
          values: [{ userEnteredValue: '0' }]
        },
        format: {
          textFormat: { foregroundColor: { red: 0, green: 0.7, blue: 0 } }
        }
      }
    };
  }
}

module.exports = FormulaBuilder;
