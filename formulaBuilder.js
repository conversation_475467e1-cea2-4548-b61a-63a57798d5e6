// formulaBuilder.js

/**
 * FormulaBuilder class generates Google Sheets formulas for budget calculations
 */
class FormulaBuilder {
  constructor() {
    this.currentYear = new Date().getFullYear();
  }

  /**
   * Generate monthly summary formulas for income, expenses, and net cash flow
   * @param {string} sheetName - Name of the monthly sheet
   * @param {string} dataRange - Range containing transaction data (e.g., "A2:G1000")
   * @returns {Object} Object containing summary formulas
   */
  getMonthlyTotalFormulas(sheetName, dataRange = "A2:G1000") {
    return {
      totalIncome: `=SUMIF(${dataRange.split(':')[0].replace(/[A-Z]/g, 'E')}:${dataRange.split(':')[1].replace(/[A-Z]/g, 'E')},"Income",${dataRange.split(':')[0].replace(/[A-Z]/g, 'D')}:${dataRange.split(':')[1].replace(/[A-Z]/g, 'D')})`,
      totalExpenses: `=SUMIF(${dataRange.split(':')[0].replace(/[A-Z]/g, 'E')}:${dataRange.split(':')[1].replace(/[A-Z]/g, 'E')},"Expense",${dataRange.split(':')[0].replace(/[A-Z]/g, 'D')}:${dataRange.split(':')[1].replace(/[A-Z]/g, 'D')})`,
      netCashFlow: `=SUMIF(${dataRange.split(':')[0].replace(/[A-Z]/g, 'E')}:${dataRange.split(':')[1].replace(/[A-Z]/g, 'E')},"Income",${dataRange.split(':')[0].replace(/[A-Z]/g, 'D')}:${dataRange.split(':')[1].replace(/[A-Z]/g, 'D')})-SUMIF(${dataRange.split(':')[0].replace(/[A-Z]/g, 'E')}:${dataRange.split(':')[1].replace(/[A-Z]/g, 'E')},"Expense",${dataRange.split(':')[0].replace(/[A-Z]/g, 'D')}:${dataRange.split(':')[1].replace(/[A-Z]/g, 'D')})`
    };
  }

  /**
   * Generate recurring transaction auto-population formula
   * @param {number} row - Row number for the formula
   * @param {string} month - Month name (e.g., "January")
   * @returns {Object} Object containing ARRAYFORMULA for recurring transactions
   */
  getRecurringTransactionFormula(row, month) {
    const monthNum = this.getMonthNumber(month);
    return {
      arrayFormula: `=ARRAYFORMULA(IF(ISBLANK(Recurring!A2:A),"",(IF(AND(Recurring!F2:F<=${this.currentYear}&"-"&${monthNum}&"-01",OR(ISBLANK(Recurring!G2:G),Recurring!G2:G>=${this.currentYear}&"-"&${monthNum}&"-01"),Recurring!H2:H=TRUE),{Recurring!B2:B,Recurring!C2:C,Recurring!D2:D,Recurring!E2:E,"Pending","Auto-populated"},""))`
    };
  }

  /**
   * Generate budget vs actual comparison formulas
   * @param {string} category - Budget category
   * @param {string} monthlyDataRange - Range of monthly transaction data
   * @returns {Object} Budget comparison formulas
   */
  getBudgetComparisonFormulas(category, monthlyDataRange = "A2:G1000") {
    return {
      budgetAmount: `=INDEX(Settings!C:C,MATCH("${category}",Settings!B:B,0))`,
      actualAmount: `=SUMIFS(${monthlyDataRange.split(':')[0].replace(/[A-Z]/g, 'D')}:${monthlyDataRange.split(':')[1].replace(/[A-Z]/g, 'D')},${monthlyDataRange.split(':')[0].replace(/[A-Z]/g, 'C')}:${monthlyDataRange.split(':')[1].replace(/[A-Z]/g, 'C')},"${category}",${monthlyDataRange.split(':')[0].replace(/[A-Z]/g, 'E')}:${monthlyDataRange.split(':')[1].replace(/[A-Z]/g, 'E')},"Expense")`,
      variance: `=INDEX(Settings!C:C,MATCH("${category}",Settings!B:B,0))-SUMIFS(${monthlyDataRange.split(':')[0].replace(/[A-Z]/g, 'D')}:${monthlyDataRange.split(':')[1].replace(/[A-Z]/g, 'D')},${monthlyDataRange.split(':')[0].replace(/[A-Z]/g, 'C')}:${monthlyDataRange.split(':')[1].replace(/[A-Z]/g, 'C')},"${category}",${monthlyDataRange.split(':')[0].replace(/[A-Z]/g, 'E')}:${monthlyDataRange.split(':')[1].replace(/[A-Z]/g, 'E')},"Expense")`,
      percentUsed: `=SUMIFS(${monthlyDataRange.split(':')[0].replace(/[A-Z]/g, 'D')}:${monthlyDataRange.split(':')[1].replace(/[A-Z]/g, 'D')},${monthlyDataRange.split(':')[0].replace(/[A-Z]/g, 'C')}:${monthlyDataRange.split(':')[1].replace(/[A-Z]/g, 'C')},"${category}",${monthlyDataRange.split(':')[0].replace(/[A-Z]/g, 'E')}:${monthlyDataRange.split(':')[1].replace(/[A-Z]/g, 'E')},"Expense")/INDEX(Settings!C:C,MATCH("${category}",Settings!B:B,0))`
    };
  }

  /**
   * Generate annual dashboard summary formulas
   * @returns {Object} Annual dashboard formulas
   */
  getAnnualDashboardFormulas() {
    const months = this.getMonthNames();
    
    return {
      ytdIncome: `=${months.map(month => `${month}!D2`).join('+')}`,
      ytdExpenses: `=${months.map(month => `${month}!D3`).join('+')}`,
      ytdNet: `=${months.map(month => `${month}!D4`).join('+')}`,
      monthlyIncomeRange: `={${months.map(month => `${month}!D2`).join(';')}}`,
      monthlyExpenseRange: `={${months.map(month => `${month}!D3`).join(';')}}`,
      monthlyNetRange: `={${months.map(month => `${month}!D4`).join(';')}}`
    };
  }

  /**
   * Generate debt tracking formulas
   * @param {number} row - Row number for calculations
   * @returns {Object} Debt calculation formulas
   */
  getDebtTrackingFormulas(row) {
    return {
      monthlyPayment: `=D${row}`, // Minimum payment from user input
      remainingPayments: `=IF(D${row}=0,0,ROUNDUP(C${row}/D${row},0))`,
      payoffDate: `=IF(D${row}=0,"",EDATE(TODAY(),ROUNDUP(C${row}/D${row},0)))`,
      totalInterest: `=IF(D${row}=0,0,(ROUNDUP(C${row}/D${row},0)*D${row})-C${row})`,
      annualTotal: `=D${row}*12`
    };
  }

  /**
   * Generate savings tracking formulas
   * @param {number} row - Row number for calculations
   * @returns {Object} Savings calculation formulas
   */
  getSavingsTrackingFormulas(row) {
    return {
      progressPercent: `=IF(B${row}=0,0,C${row}/B${row})`,
      remainingAmount: `=B${row}-C${row}`,
      monthsToGoal: `=IF(D${row}=0,0,ROUNDUP((B${row}-C${row})/D${row},0))`,
      projectedDate: `=IF(D${row}=0,"",EDATE(TODAY(),ROUNDUP((B${row}-C${row})/D${row},0)))`,
      onTrack: `=IF(E${row}="","",IF(EDATE(TODAY(),ROUNDUP((B${row}-C${row})/D${row},0))<=E${row},"On Track","Behind"))`
    };
  }

  /**
   * Generate subscription tracking formulas
   * @param {number} row - Row number for calculations
   * @returns {Object} Subscription calculation formulas
   */
  getSubscriptionFormulas(row) {
    return {
      nextDueDate: `=IF(C${row}="Monthly",EDATE(D${row},1),IF(C${row}="Quarterly",EDATE(D${row},3),IF(C${row}="Annually",EDATE(D${row},12),D${row})))`,
      annualTotal: `=IF(C${row}="Monthly",B${row}*12,IF(C${row}="Quarterly",B${row}*4,IF(C${row}="Annually",B${row},B${row}*12)))`,
      daysUntilDue: `=IF(C${row}="Monthly",EDATE(D${row},1)-TODAY(),IF(C${row}="Quarterly",EDATE(D${row},3)-TODAY(),IF(C${row}="Annually",EDATE(D${row},12)-TODAY(),D${row}-TODAY())))`
    };
  }

  /**
   * Generate data validation formulas for dropdowns
   * @returns {Object} Data validation ranges
   */
  getDataValidationRanges() {
    return {
      categories: "Categories!A:A",
      transactionTypes: "Settings!E2:E4", // Income, Expense, Transfer
      transactionStatus: "Settings!F2:F4", // Pending, Cleared, Reconciled
      billingFrequency: "Settings!G2:G5" // Monthly, Quarterly, Annually, One-time
    };
  }

  /**
   * Helper method to get month number from month name
   * @param {string} monthName - Month name (e.g., "January")
   * @returns {number} Month number (1-12)
   */
  getMonthNumber(monthName) {
    const months = {
      'January': 1, 'February': 2, 'March': 3, 'April': 4,
      'May': 5, 'June': 6, 'July': 7, 'August': 8,
      'September': 9, 'October': 10, 'November': 11, 'December': 12
    };
    return months[monthName] || 1;
  }

  /**
   * Helper method to get all month names
   * @returns {Array<string>} Array of month names
   */
  getMonthNames() {
    return ['January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'];
  }

  /**
   * Generate conditional formatting rules for amounts
   * @returns {Object} Conditional formatting rules
   */
  getConditionalFormattingRules() {
    return {
      negativeAmounts: {
        condition: {
          type: 'NUMBER_LESS',
          values: [{ userEnteredValue: '0' }]
        },
        format: {
          textFormat: { foregroundColor: { red: 1, green: 0, blue: 0 } }
        }
      },
      positiveAmounts: {
        condition: {
          type: 'NUMBER_GREATER',
          values: [{ userEnteredValue: '0' }]
        },
        format: {
          textFormat: { foregroundColor: { red: 0, green: 0.7, blue: 0 } }
        }
      }
    };
  }
}

module.exports = FormulaBuilder;
