/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { acmedns_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof acmedns_v1.Acmedns;
};
export declare function acmedns(version: 'v1'): acmedns_v1.Acmedns;
export declare function acmedns(options: acmedns_v1.Options): acmedns_v1.Acmedns;
declare const auth: AuthPlus;
export { auth };
export { acmedns_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
