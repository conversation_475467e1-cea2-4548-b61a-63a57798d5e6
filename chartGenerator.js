// chartGenerator.js

/**
 * ChartGenerator class creates charts and visualizations for the budget workbook
 */
class ChartGenerator {
  constructor(sheets, spreadsheetId) {
    this.sheets = sheets;
    this.spreadsheetId = spreadsheetId;
  }

  /**
   * Create a column chart for monthly cash flow
   * @param {string} sheetName - Sheet to place the chart
   * @param {Object} position - Chart position {row, column}
   * @param {string} dataRange - Range containing the data
   * @param {string} title - Chart title
   * @returns {Promise<void>}
   */
  async createMonthlyCashFlowChart(sheetName, position, dataRange, title = "Monthly Cash Flow") {
    try {
      const sheetId = await this.getSheetId(sheetName);
      
      const request = {
        spreadsheetId: this.spreadsheetId,
        resource: {
          requests: [{
            addChart: {
              chart: {
                spec: {
                  title: title,
                  basicChart: {
                    chartType: 'COLUMN',
                    legendPosition: 'BOTTOM_LEGEND',
                    axis: [
                      {
                        position: 'BOTTOM_AXIS',
                        title: 'Month'
                      },
                      {
                        position: 'LEFT_AXIS',
                        title: 'Amount ($)'
                      }
                    ],
                    domains: [{
                      domain: {
                        sourceRange: {
                          sources: [this.convertA1ToGridRange(dataRange.split(',')[0], sheetId)]
                        }
                      }
                    }],
                    series: [
                      {
                        series: {
                          sourceRange: {
                            sources: [this.convertA1ToGridRange(dataRange.split(',')[1], sheetId)]
                          }
                        },
                        targetAxis: 'LEFT_AXIS',
                        type: 'COLUMN',
                        color: { red: 0.2, green: 0.6, blue: 0.9 }
                      },
                      {
                        series: {
                          sourceRange: {
                            sources: [this.convertA1ToGridRange(dataRange.split(',')[2], sheetId)]
                          }
                        },
                        targetAxis: 'LEFT_AXIS',
                        type: 'COLUMN',
                        color: { red: 0.9, green: 0.3, blue: 0.3 }
                      },
                      {
                        series: {
                          sourceRange: {
                            sources: [this.convertA1ToGridRange(dataRange.split(',')[3], sheetId)]
                          }
                        },
                        targetAxis: 'LEFT_AXIS',
                        type: 'COLUMN',
                        color: { red: 0.2, green: 0.7, blue: 0.2 }
                      }
                    ]
                  }
                },
                position: {
                  overlayPosition: {
                    anchorCell: {
                      sheetId: sheetId,
                      rowIndex: position.row,
                      columnIndex: position.column
                    },
                    offsetXPixels: 0,
                    offsetYPixels: 0,
                    widthPixels: 600,
                    heightPixels: 400
                  }
                }
              }
            }
          }]
        }
      };

      await this.sheets.spreadsheets.batchUpdate(request);
      console.log(`✅ Created monthly cash flow chart in ${sheetName}`);
    } catch (error) {
      console.error(`❌ Failed to create monthly cash flow chart:`, error.message);
      throw error;
    }
  }

  /**
   * Create a pie chart for expense categories
   * @param {string} sheetName - Sheet to place the chart
   * @param {Object} position - Chart position {row, column}
   * @param {string} categoriesRange - Range containing category names
   * @param {string} amountsRange - Range containing amounts
   * @param {string} title - Chart title
   * @returns {Promise<void>}
   */
  async createExpenseCategoryPieChart(sheetName, position, categoriesRange, amountsRange, title = "Expense Categories") {
    try {
      const sheetId = await this.getSheetId(sheetName);
      
      const request = {
        spreadsheetId: this.spreadsheetId,
        resource: {
          requests: [{
            addChart: {
              chart: {
                spec: {
                  title: title,
                  pieChart: {
                    legendPosition: 'RIGHT_LEGEND',
                    domain: {
                      sourceRange: {
                        sources: [this.convertA1ToGridRange(categoriesRange, sheetId)]
                      }
                    },
                    series: {
                      sourceRange: {
                        sources: [this.convertA1ToGridRange(amountsRange, sheetId)]
                      }
                    }
                  }
                },
                position: {
                  overlayPosition: {
                    anchorCell: {
                      sheetId: sheetId,
                      rowIndex: position.row,
                      columnIndex: position.column
                    },
                    offsetXPixels: 0,
                    offsetYPixels: 0,
                    widthPixels: 500,
                    heightPixels: 350
                  }
                }
              }
            }
          }]
        }
      };

      await this.sheets.spreadsheets.batchUpdate(request);
      console.log(`✅ Created expense category pie chart in ${sheetName}`);
    } catch (error) {
      console.error(`❌ Failed to create expense category pie chart:`, error.message);
      throw error;
    }
  }

  /**
   * Create a line chart for savings progress
   * @param {string} sheetName - Sheet to place the chart
   * @param {Object} position - Chart position {row, column}
   * @param {string} monthsRange - Range containing months
   * @param {string} savingsRange - Range containing savings amounts
   * @param {string} title - Chart title
   * @returns {Promise<void>}
   */
  async createSavingsProgressChart(sheetName, position, monthsRange, savingsRange, title = "Savings Progress") {
    try {
      const sheetId = await this.getSheetId(sheetName);
      
      const request = {
        spreadsheetId: this.spreadsheetId,
        resource: {
          requests: [{
            addChart: {
              chart: {
                spec: {
                  title: title,
                  basicChart: {
                    chartType: 'LINE',
                    legendPosition: 'BOTTOM_LEGEND',
                    axis: [
                      {
                        position: 'BOTTOM_AXIS',
                        title: 'Month'
                      },
                      {
                        position: 'LEFT_AXIS',
                        title: 'Savings ($)'
                      }
                    ],
                    domains: [{
                      domain: {
                        sourceRange: {
                          sources: [this.convertA1ToGridRange(monthsRange, sheetId)]
                        }
                      }
                    }],
                    series: [{
                      series: {
                        sourceRange: {
                          sources: [this.convertA1ToGridRange(savingsRange, sheetId)]
                        }
                      },
                      targetAxis: 'LEFT_AXIS',
                      type: 'LINE',
                      color: { red: 0.2, green: 0.7, blue: 0.2 }
                    }]
                  }
                },
                position: {
                  overlayPosition: {
                    anchorCell: {
                      sheetId: sheetId,
                      rowIndex: position.row,
                      columnIndex: position.column
                    },
                    offsetXPixels: 0,
                    offsetYPixels: 0,
                    widthPixels: 600,
                    heightPixels: 300
                  }
                }
              }
            }
          }]
        }
      };

      await this.sheets.spreadsheets.batchUpdate(request);
      console.log(`✅ Created savings progress chart in ${sheetName}`);
    } catch (error) {
      console.error(`❌ Failed to create savings progress chart:`, error.message);
      throw error;
    }
  }

  /**
   * Create sparklines for trend analysis
   * @param {string} sheetName - Sheet to place sparklines
   * @param {string} cell - Cell to place sparkline
   * @param {string} dataRange - Range containing data for sparkline
   * @param {string} type - Sparkline type ('line', 'column', 'winloss')
   * @returns {Promise<void>}
   */
  async createSparkline(sheetName, cell, dataRange, type = 'line') {
    try {
      const sparklineFormula = `=SPARKLINE(${dataRange},"charttype","${type}")`;
      
      await this.sheets.spreadsheets.values.update({
        spreadsheetId: this.spreadsheetId,
        range: `${sheetName}!${cell}`,
        valueInputOption: 'USER_ENTERED',
        resource: {
          values: [[sparklineFormula]]
        }
      });

      console.log(`✅ Created ${type} sparkline in ${sheetName}!${cell}`);
    } catch (error) {
      console.error(`❌ Failed to create sparkline:`, error.message);
      throw error;
    }
  }

  /**
   * Create multiple sparklines for dashboard
   * @param {string} sheetName - Dashboard sheet name
   * @returns {Promise<void>}
   */
  async createDashboardSparklines(sheetName) {
    try {
      console.log(`Creating sparklines for ${sheetName}...`);

      // Income trend sparkline
      await this.createSparkline(sheetName, 'F5', 'B5:M5', 'line');
      
      // Expense trend sparkline
      await this.createSparkline(sheetName, 'F6', 'B6:M6', 'line');
      
      // Net cash flow sparkline
      await this.createSparkline(sheetName, 'F7', 'B7:M7', 'column');
      
      // Savings progress sparkline
      await this.createSparkline(sheetName, 'F8', 'B8:M8', 'line');

      console.log(`✅ Created all dashboard sparklines`);
    } catch (error) {
      console.error(`❌ Failed to create dashboard sparklines:`, error.message);
      throw error;
    }
  }

  /**
   * Helper method to get sheet ID by name
   * @param {string} sheetName - Name of the sheet
   * @returns {Promise<number>} Sheet ID
   */
  async getSheetId(sheetName) {
    const response = await this.sheets.spreadsheets.get({
      spreadsheetId: this.spreadsheetId
    });
    
    const sheet = response.data.sheets.find(s => s.properties.title === sheetName);
    if (!sheet) {
      throw new Error(`Sheet "${sheetName}" not found`);
    }
    
    return sheet.properties.sheetId;
  }

  /**
   * Convert A1 notation to GridRange object
   * @param {string} a1Range - A1 notation range
   * @param {number} sheetId - Sheet ID
   * @returns {Object} GridRange object
   */
  convertA1ToGridRange(a1Range, sheetId) {
    const [start, end] = a1Range.split(':');
    
    const startCol = this.columnToIndex(start.replace(/\d+/g, ''));
    const startRow = parseInt(start.replace(/[A-Z]+/g, '')) - 1;
    
    let endCol, endRow;
    if (end) {
      endCol = this.columnToIndex(end.replace(/\d+/g, '')) + 1;
      endRow = parseInt(end.replace(/[A-Z]+/g, ''));
    } else {
      endCol = startCol + 1;
      endRow = startRow + 1;
    }

    return {
      sheetId: sheetId,
      startRowIndex: startRow,
      endRowIndex: endRow,
      startColumnIndex: startCol,
      endColumnIndex: endCol
    };
  }

  /**
   * Convert column letter to index
   * @param {string} column - Column letter
   * @returns {number} Column index (0-based)
   */
  columnToIndex(column) {
    let result = 0;
    for (let i = 0; i < column.length; i++) {
      result = result * 26 + (column.charCodeAt(i) - 'A'.charCodeAt(0) + 1);
    }
    return result - 1;
  }
}

module.exports = ChartGenerator;
