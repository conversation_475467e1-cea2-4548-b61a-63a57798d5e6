// sheetCreator.js
const { google } = require("googleapis");

/**
 * SheetCreator class handles creation and management of Google Sheets
 */
class SheetCreator {
  constructor(sheets, spreadsheetId) {
    this.sheets = sheets;
    this.spreadsheetId = spreadsheetId;
  }

  /**
   * Create a new sheet with specified properties
   * @param {string} title - Sheet title
   * @param {number} rowCount - Number of rows (default: 1000)
   * @param {number} columnCount - Number of columns (default: 26)
   * @param {boolean} hidden - Whether sheet should be hidden (default: false)
   * @returns {Promise<Object>} Sheet creation response
   */
  async createSheet(title, rowCount = 1000, columnCount = 26, hidden = false) {
    try {
      console.log(`Creating sheet: ${title}`);
      
      const request = {
        spreadsheetId: this.spreadsheetId,
        resource: {
          requests: [{
            addSheet: {
              properties: {
                title: title,
                gridProperties: {
                  rowCount: rowCount,
                  columnCount: columnCount
                },
                hidden: hidden
              }
            }
          }]
        }
      };

      const response = await this.sheets.spreadsheets.batchUpdate(request);
      console.log(`✅ Created sheet: ${title}`);
      return response.data.replies[0].addSheet.properties;
    } catch (error) {
      console.error(`❌ Failed to create sheet ${title}:`, error.message);
      throw error;
    }
  }

  /**
   * Delete a sheet by title
   * @param {string} title - Sheet title to delete
   * @returns {Promise<void>}
   */
  async deleteSheet(title) {
    try {
      console.log(`Deleting sheet: ${title}`);
      
      // First get the sheet ID
      const spreadsheet = await this.sheets.spreadsheets.get({
        spreadsheetId: this.spreadsheetId
      });
      
      const sheet = spreadsheet.data.sheets.find(s => s.properties.title === title);
      if (!sheet) {
        console.log(`Sheet ${title} not found, skipping deletion`);
        return;
      }

      const request = {
        spreadsheetId: this.spreadsheetId,
        resource: {
          requests: [{
            deleteSheet: {
              sheetId: sheet.properties.sheetId
            }
          }]
        }
      };

      await this.sheets.spreadsheets.batchUpdate(request);
      console.log(`✅ Deleted sheet: ${title}`);
    } catch (error) {
      console.error(`❌ Failed to delete sheet ${title}:`, error.message);
      throw error;
    }
  }

  /**
   * Create multiple sheets in batch
   * @param {Array<Object>} sheetConfigs - Array of sheet configurations
   * @returns {Promise<Array>} Array of created sheet properties
   */
  async createMultipleSheets(sheetConfigs) {
    const requests = sheetConfigs.map(config => ({
      addSheet: {
        properties: {
          title: config.title,
          gridProperties: {
            rowCount: config.rowCount || 1000,
            columnCount: config.columnCount || 26
          },
          hidden: config.hidden || false
        }
      }
    }));

    try {
      console.log(`Creating ${sheetConfigs.length} sheets in batch...`);
      
      const response = await this.sheets.spreadsheets.batchUpdate({
        spreadsheetId: this.spreadsheetId,
        resource: { requests }
      });

      const createdSheets = response.data.replies.map(reply => reply.addSheet.properties);
      console.log(`✅ Created ${createdSheets.length} sheets successfully`);
      
      return createdSheets;
    } catch (error) {
      console.error(`❌ Failed to create multiple sheets:`, error.message);
      throw error;
    }
  }

  /**
   * Get all existing sheets in the spreadsheet
   * @returns {Promise<Array>} Array of sheet properties
   */
  async getExistingSheets() {
    try {
      const response = await this.sheets.spreadsheets.get({
        spreadsheetId: this.spreadsheetId
      });
      return response.data.sheets.map(sheet => sheet.properties);
    } catch (error) {
      console.error(`❌ Failed to get existing sheets:`, error.message);
      throw error;
    }
  }

  /**
   * Update sheet properties (rename, hide/show, etc.)
   * @param {number} sheetId - Sheet ID to update
   * @param {Object} properties - Properties to update
   * @returns {Promise<void>}
   */
  async updateSheetProperties(sheetId, properties) {
    try {
      const request = {
        spreadsheetId: this.spreadsheetId,
        resource: {
          requests: [{
            updateSheetProperties: {
              properties: {
                sheetId: sheetId,
                ...properties
              },
              fields: Object.keys(properties).join(',')
            }
          }]
        }
      };

      await this.sheets.spreadsheets.batchUpdate(request);
      console.log(`✅ Updated sheet properties for sheet ID: ${sheetId}`);
    } catch (error) {
      console.error(`❌ Failed to update sheet properties:`, error.message);
      throw error;
    }
  }

  /**
   * Write data to a specific range in a sheet
   * @param {string} range - A1 notation range (e.g., "Sheet1!A1:C3")
   * @param {Array<Array>} values - 2D array of values to write
   * @param {string} valueInputOption - How to interpret input (RAW, USER_ENTERED)
   * @returns {Promise<Object>} Update response
   */
  async writeData(range, values, valueInputOption = 'USER_ENTERED') {
    try {
      const response = await this.sheets.spreadsheets.values.update({
        spreadsheetId: this.spreadsheetId,
        range: range,
        valueInputOption: valueInputOption,
        resource: {
          values: values
        }
      });

      console.log(`✅ Wrote data to range: ${range} (${response.data.updatedCells} cells)`);
      return response.data;
    } catch (error) {
      console.error(`❌ Failed to write data to ${range}:`, error.message);
      throw error;
    }
  }

  /**
   * Read data from a specific range
   * @param {string} range - A1 notation range
   * @returns {Promise<Array<Array>|null>} 2D array of values or null if no data
   */
  async readData(range) {
    try {
      const response = await this.sheets.spreadsheets.values.get({
        spreadsheetId: this.spreadsheetId,
        range: range
      });

      return response.data.values || null;
    } catch (error) {
      console.error(`❌ Failed to read data from ${range}:`, error.message);
      throw error;
    }
  }
}

module.exports = SheetCreator;
