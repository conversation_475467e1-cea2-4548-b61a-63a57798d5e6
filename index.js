// index.js - Budget Workbook Generator
const { google } = require("googleapis");
const path = require("path");
const fs = require("fs");

// Import our modular components
const SheetCreator = require('./sheetCreator');
const FormulaBuilder = require('./formulaBuilder');
const DataValidator = require('./dataValidator');
const ChartGenerator = require('./chartGenerator');

/**
 * Dynamically discovers the first JSON key file in the credentials directory
 * @returns {string} Path to the service account JSON key file
 */
function findServiceAccountKey() {
  const credentialsDir = path.join(__dirname, "credentials");

  try {
    // Read all files in the credentials directory
    const files = fs.readdirSync(credentialsDir);

    // Find the first .json file
    const jsonFile = files.find(file => file.endsWith('.json'));

    if (!jsonFile) {
      throw new Error("No JSON key file found in credentials directory");
    }

    const keyFilePath = path.join(credentialsDir, jsonFile);
    console.log(`Using service account key: ${jsonFile}`);

    return keyFilePath;
  } catch (error) {
    throw new Error(`Failed to find service account key: ${error.message}`);
  }
}

/**
 * Main function to generate comprehensive budget workbook
 */
async function generateBudgetWorkbook() {
  try {
    // Dynamically discover the service account key file
    const keyFile = findServiceAccountKey();

    // Load and authorize with your service account key
    const auth = new google.auth.GoogleAuth({
      keyFile: keyFile,
      scopes: ["https://www.googleapis.com/auth/spreadsheets"],
    });

    const client = await auth.getClient();
    const sheets = google.sheets({ version: "v4", auth: client });

    // Fixed spreadsheet ID format (removed the /edit?gid=0#gid=0 part)
    const spreadsheetId = "10qvxabtNjCKZKP-WinKAgMynS_Js6gZRn3ZzPzLsT_I";

    console.log(`🚀 Starting Budget Workbook Generation...`);
    console.log(`📊 Spreadsheet ID: ${spreadsheetId}`);

    // Initialize our helper classes
    const sheetCreator = new SheetCreator(sheets, spreadsheetId);
    const formulaBuilder = new FormulaBuilder();
    const dataValidator = new DataValidator(sheets, spreadsheetId);
    const chartGenerator = new ChartGenerator(sheets, spreadsheetId);

    // Generate the complete budget workbook
    await createBudgetWorkbook(sheetCreator, formulaBuilder, dataValidator, chartGenerator);

    console.log(`✅ Budget Workbook Generation Complete!`);

    console.log(`✅ Budget Workbook Generation Complete!`);
  } catch (error) {
    console.error("❌ Error occurred:", error.message);
    if (error.code) {
      console.error("Error code:", error.code);
    }
  }
}

/**
 * Create the complete budget workbook structure
 */
async function createBudgetWorkbook(sheetCreator, formulaBuilder, dataValidator, chartGenerator) {
  console.log(`\n📋 Phase 1: Setting up sheet structure...`);

  // Get current spreadsheet info
  const existingSheets = await sheetCreator.getExistingSheets();
  console.log(`Found ${existingSheets.length} existing sheets`);

  // Remove test data from Sheet1 if it exists
  if (existingSheets.find(s => s.title === 'Sheet1')) {
    console.log(`Clearing test data from Sheet1...`);
    await sheetCreator.writeData('Sheet1!A1:Z1000', []);
  }

  // Define the sheets we need to create
  const sheetsToCreate = [
    // Monthly sheets
    { title: 'January', rowCount: 1000, columnCount: 10 },
    { title: 'February', rowCount: 1000, columnCount: 10 },
    { title: 'March', rowCount: 1000, columnCount: 10 },
    { title: 'April', rowCount: 1000, columnCount: 10 },
    { title: 'May', rowCount: 1000, columnCount: 10 },
    { title: 'June', rowCount: 1000, columnCount: 10 },
    { title: 'July', rowCount: 1000, columnCount: 10 },
    { title: 'August', rowCount: 1000, columnCount: 10 },
    { title: 'September', rowCount: 1000, columnCount: 10 },
    { title: 'October', rowCount: 1000, columnCount: 10 },
    { title: 'November', rowCount: 1000, columnCount: 10 },
    { title: 'December', rowCount: 1000, columnCount: 10 },

    // Dashboard and analysis sheets
    { title: 'Annual Dashboard', rowCount: 100, columnCount: 20 },
    { title: 'Bills Calendar', rowCount: 50, columnCount: 15 },

    // Configuration sheets (hidden)
    { title: 'Categories', rowCount: 100, columnCount: 5, hidden: true },
    { title: 'Settings', rowCount: 50, columnCount: 10, hidden: true },
    { title: 'Recurring', rowCount: 100, columnCount: 10, hidden: true },

    // Tracking sheets
    { title: 'Subscriptions', rowCount: 50, columnCount: 8 },
    { title: 'Debt', rowCount: 20, columnCount: 10 },
    { title: 'Savings', rowCount: 20, columnCount: 8 }
  ];

  // Filter out sheets that already exist
  const newSheets = sheetsToCreate.filter(newSheet =>
    !existingSheets.find(existing => existing.title === newSheet.title)
  );

  if (newSheets.length > 0) {
    console.log(`Creating ${newSheets.length} new sheets...`);
    await sheetCreator.createMultipleSheets(newSheets);
  } else {
    console.log(`All required sheets already exist.`);
  }

  console.log(`\n📊 Phase 2: Setting up configuration data...`);
  await setupConfigurationSheets(sheetCreator, formulaBuilder);

  console.log(`\n📅 Phase 3: Creating monthly sheet templates...`);
  await setupMonthlySheets(sheetCreator, formulaBuilder, dataValidator);

  console.log(`\n📈 Phase 4: Setting up tracking sheets...`);
  await setupTrackingSheets(sheetCreator, formulaBuilder, dataValidator);

  console.log(`\n🎯 Phase 5: Creating annual dashboard...`);
  await setupAnnualDashboard(sheetCreator, formulaBuilder, chartGenerator);

  console.log(`\n📋 Phase 6: Setting up bills calendar...`);
  await setupBillsCalendar(sheetCreator, formulaBuilder);

  console.log(`\n🎨 Phase 7: Applying formatting and validation...`);
  await applyFormattingAndValidation(dataValidator);

  console.log(`\n📝 Phase 8: Adding sample data...`);
  await addSampleData(sheetCreator);
}

/**
 * Setup configuration sheets with categories, settings, and recurring transactions
 */
async function setupConfigurationSheets(sheetCreator, formulaBuilder) {
  // Categories sheet
  const categories = [
    ['Category', 'Type', 'Budget Amount', 'Description'],
    ['Housing', 'Expense', '1500', 'Rent, mortgage, utilities'],
    ['Transportation', 'Expense', '400', 'Car payment, gas, insurance'],
    ['Food & Dining', 'Expense', '600', 'Groceries, restaurants'],
    ['Utilities', 'Expense', '200', 'Electric, water, internet'],
    ['Healthcare', 'Expense', '300', 'Insurance, medical expenses'],
    ['Entertainment', 'Expense', '200', 'Movies, subscriptions, hobbies'],
    ['Shopping', 'Expense', '300', 'Clothing, personal items'],
    ['Personal Care', 'Expense', '100', 'Haircuts, cosmetics'],
    ['Education', 'Expense', '150', 'Books, courses, training'],
    ['Savings', 'Transfer', '500', 'Emergency fund, investments'],
    ['Salary', 'Income', '5000', 'Primary income'],
    ['Side Income', 'Income', '500', 'Freelance, part-time work'],
    ['Investment Income', 'Income', '100', 'Dividends, interest']
  ];

  await sheetCreator.writeData('Categories!A1:D14', categories);

  // Settings sheet
  const settings = [
    ['Setting', 'Value', 'Description'],
    ['Currency', 'USD', 'Default currency'],
    ['Date Format', 'MM/DD/YYYY', 'Date display format'],
    ['First Day of Week', 'Sunday', 'Calendar start day'],
    ['Budget Period', 'Monthly', 'Budget calculation period'],
    ['', '', ''],
    ['Transaction Types', '', ''],
    ['Income', '', ''],
    ['Expense', '', ''],
    ['Transfer', '', ''],
    ['', '', ''],
    ['Transaction Status', '', ''],
    ['Pending', '', ''],
    ['Cleared', '', ''],
    ['Reconciled', '', ''],
    ['', '', ''],
    ['Billing Frequency', '', ''],
    ['Monthly', '', ''],
    ['Quarterly', '', ''],
    ['Annually', '', ''],
    ['One-time', '', '']
  ];

  await sheetCreator.writeData('Settings!A1:C21', settings);

  // Recurring transactions template
  const recurringHeaders = [
    ['ID', 'Description', 'Category', 'Amount', 'Type', 'Start Date', 'End Date', 'Active', 'Frequency', 'Notes']
  ];

  await sheetCreator.writeData('Recurring!A1:J1', recurringHeaders);

  console.log(`✅ Configuration sheets setup complete`);
}

/**
 * Setup monthly sheet templates with headers and formulas
 */
async function setupMonthlySheets(sheetCreator, formulaBuilder, dataValidator) {
  const months = formulaBuilder.getMonthNames();

  for (const month of months) {
    // Headers for monthly sheets
    const headers = [
      ['Date', 'Description', 'Category', 'Amount', 'Type', 'Status', 'Account', 'Notes', 'Balance', 'Running Total']
    ];

    await sheetCreator.writeData(`${month}!A1:J1`, headers);

    // Summary section headers
    const summaryHeaders = [
      ['', 'MONTHLY SUMMARY', '', ''],
      ['Total Income:', '=SUMIF(E:E,"Income",D:D)', '', ''],
      ['Total Expenses:', '=SUMIF(E:E,"Expense",D:D)', '', ''],
      ['Net Cash Flow:', '=B2-B3', '', ''],
      ['', '', '', ''],
      ['BUDGET vs ACTUAL', '', '', ''],
      ['Category', 'Budget', 'Actual', 'Variance']
    ];

    await sheetCreator.writeData(`${month}!A${1002}:D${1009}`, summaryHeaders);

    console.log(`✅ Setup ${month} sheet template`);
  }
}

/**
 * Setup tracking sheets for subscriptions, debt, and savings
 */
async function setupTrackingSheets(sheetCreator, formulaBuilder, dataValidator) {
  // Subscriptions sheet
  const subscriptionHeaders = [
    ['Service', 'Amount', 'Frequency', 'Last Paid', 'Next Due', 'Annual Total', 'Days Until Due', 'Notes']
  ];
  await sheetCreator.writeData('Subscriptions!A1:H1', subscriptionHeaders);

  // Debt sheet
  const debtHeaders = [
    ['Debt Name', 'Balance', 'Min Payment', 'Interest Rate', 'Remaining Payments', 'Payoff Date', 'Total Interest', 'Annual Total', 'Notes']
  ];
  await sheetCreator.writeData('Debt!A1:I1', debtHeaders);

  // Savings sheet
  const savingsHeaders = [
    ['Goal Name', 'Target Amount', 'Current Amount', 'Monthly Contribution', 'Target Date', 'Progress %', 'Remaining', 'Months to Goal']
  ];
  await sheetCreator.writeData('Savings!A1:H1', savingsHeaders);

  console.log(`✅ Tracking sheets setup complete`);
}

/**
 * Setup annual dashboard with summary data and charts
 */
async function setupAnnualDashboard(sheetCreator, formulaBuilder, chartGenerator) {
  const dashboardData = [
    ['ANNUAL BUDGET DASHBOARD', '', '', '', '', '', '', '', '', '', '', '', ''],
    ['', '', '', '', '', '', '', '', '', '', '', '', ''],
    ['MONTHLY SUMMARY', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    ['Income', '=January!B2', '=February!B2', '=March!B2', '=April!B2', '=May!B2', '=June!B2', '=July!B2', '=August!B2', '=September!B2', '=October!B2', '=November!B2', '=December!B2'],
    ['Expenses', '=January!B3', '=February!B3', '=March!B3', '=April!B3', '=May!B3', '=June!B3', '=July!B3', '=August!B3', '=September!B3', '=October!B3', '=November!B3', '=December!B3'],
    ['Net Cash Flow', '=January!B4', '=February!B4', '=March!B4', '=April!B4', '=May!B4', '=June!B4', '=July!B4', '=August!B4', '=September!B4', '=October!B4', '=November!B4', '=December!B4'],
    ['', '', '', '', '', '', '', '', '', '', '', '', ''],
    ['YEAR-TO-DATE TOTALS', '', '', '', '', '', '', '', '', '', '', '', ''],
    ['YTD Income:', '=SUM(B4:M4)', '', '', '', '', '', '', '', '', '', '', ''],
    ['YTD Expenses:', '=SUM(B5:M5)', '', '', '', '', '', '', '', '', '', '', ''],
    ['YTD Net:', '=SUM(B6:M6)', '', '', '', '', '', '', '', '', '', '', ''],
    ['', '', '', '', '', '', '', '', '', '', '', '', ''],
    ['KEY PERFORMANCE INDICATORS', '', '', '', '', '', '', '', '', '', '', '', ''],
    ['Average Monthly Income:', '=AVERAGE(B4:M4)', '', '', '', '', '', '', '', '', '', '', ''],
    ['Average Monthly Expenses:', '=AVERAGE(B5:M5)', '', '', '', '', '', '', '', '', '', '', ''],
    ['Savings Rate:', '=B11/B9', '', '', '', '', '', '', '', '', '', '', '']
  ];

  await sheetCreator.writeData('Annual Dashboard!A1:M16', dashboardData);
  console.log(`✅ Annual dashboard setup complete`);
}

/**
 * Setup bills calendar for tracking due dates
 */
async function setupBillsCalendar(sheetCreator, formulaBuilder) {
  const calendarHeaders = [
    ['BILLS CALENDAR', '', '', '', '', '', '', '', '', '', '', '', ''],
    ['', '', '', '', '', '', '', '', '', '', '', '', ''],
    ['Bill Name', 'Amount', 'Due Date', 'Category', 'Status', 'Auto Pay', 'Account', 'Notes', '', '', '', '', '']
  ];

  await sheetCreator.writeData('Bills Calendar!A1:M3', calendarHeaders);
  console.log(`✅ Bills calendar setup complete`);
}

/**
 * Apply formatting and validation rules
 */
async function applyFormattingAndValidation(dataValidator) {
  const months = ['January', 'February', 'March', 'April', 'May', 'June',
                  'July', 'August', 'September', 'October', 'November', 'December'];

  for (const month of months) {
    await dataValidator.applyMonthlySheetValidations(month);
    await dataValidator.applyAmountConditionalFormatting(month, 'D2:D1000');
  }

  console.log(`✅ Formatting and validation applied`);
}

/**
 * Add sample data for demonstration
 */
async function addSampleData(sheetCreator) {
  // Add sample data to January sheet
  const sampleData = [
    ['2024-01-01', 'Salary Deposit', 'Salary', '5000', 'Income', 'Cleared', 'Checking', 'Monthly salary'],
    ['2024-01-01', 'Rent Payment', 'Housing', '-1500', 'Expense', 'Cleared', 'Checking', 'Monthly rent'],
    ['2024-01-02', 'Grocery Shopping', 'Food & Dining', '-120.50', 'Expense', 'Cleared', 'Credit Card', 'Weekly groceries'],
    ['2024-01-03', 'Gas Station', 'Transportation', '-45.00', 'Expense', 'Cleared', 'Credit Card', 'Fill up tank'],
    ['2024-01-05', 'Emergency Fund', 'Savings', '-500', 'Transfer', 'Cleared', 'Savings Account', 'Monthly savings']
  ];

  await sheetCreator.writeData('January!A2:H6', sampleData);

  // Add sample recurring transactions
  const recurringData = [
    ['1', 'Monthly Salary', 'Salary', '5000', 'Income', '2024-01-01', '2024-12-31', 'TRUE', 'Monthly', 'Primary income'],
    ['2', 'Rent Payment', 'Housing', '-1500', 'Expense', '2024-01-01', '2024-12-31', 'TRUE', 'Monthly', 'Monthly rent'],
    ['3', 'Car Insurance', 'Transportation', '-150', 'Expense', '2024-01-15', '2024-12-31', 'TRUE', 'Monthly', 'Auto insurance'],
    ['4', 'Netflix Subscription', 'Entertainment', '-15.99', 'Expense', '2024-01-10', '2024-12-31', 'TRUE', 'Monthly', 'Streaming service']
  ];

  await sheetCreator.writeData('Recurring!A2:J5', recurringData);

  console.log(`✅ Sample data added`);
}

// Execute the budget workbook generation
generateBudgetWorkbook().catch(console.error);
