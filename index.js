// index.js
const { google } = require("googleapis");
const path = require("path");
const fs = require("fs");

/**
 * Dynamically discovers the first JSON key file in the credentials directory
 * @returns {string} Path to the service account JSON key file
 */
function findServiceAccountKey() {
  const credentialsDir = path.join(__dirname, "credentials");

  try {
    // Read all files in the credentials directory
    const files = fs.readdirSync(credentialsDir);

    // Find the first .json file
    const jsonFile = files.find(file => file.endsWith('.json'));

    if (!jsonFile) {
      throw new Error("No JSON key file found in credentials directory");
    }

    const keyFilePath = path.join(credentialsDir, jsonFile);
    console.log(`Using service account key: ${jsonFile}`);

    return keyFilePath;
  } catch (error) {
    throw new Error(`Failed to find service account key: ${error.message}`);
  }
}

async function listTransactions() {
  try {
    // Dynamically discover the service account key file
    const keyFile = findServiceAccountKey();

    // Load and authorize with your service account key
    const auth = new google.auth.GoogleAuth({
      keyFile: keyFile,
      scopes: ["https://www.googleapis.com/auth/spreadsheets"],
    });

    const client = await auth.getClient();
    const sheets = google.sheets({ version: "v4", auth: client });

    // Fixed spreadsheet ID format (removed the /edit?gid=0#gid=0 part)
    const spreadsheetId = "10qvxabtNjCKZKP-WinKAgMynS_Js6gZRn3ZzPzLsT_I";

    console.log(`Fetching data from spreadsheet: ${spreadsheetId}`);

    // First, get information about the spreadsheet to see available sheets
    try {
      const spreadsheetInfo = await sheets.spreadsheets.get({
        spreadsheetId,
      });

      console.log("Available sheets:");
      spreadsheetInfo.data.sheets.forEach(sheet => {
        console.log(`- ${sheet.properties.title} (ID: ${sheet.properties.sheetId})`);
      });

      // Try different possible ranges
      const possibleRanges = [
        "Bills!A1:F10",
        "Sheet1!A1:F10",
        "A1:F10"  // Default sheet range
      ];

      let dataFound = false;

      for (const range of possibleRanges) {
        try {
          console.log(`\nTrying range: ${range}`);

          const res = await sheets.spreadsheets.values.get({
            spreadsheetId,
            range,
          });

          if (res.data.values && res.data.values.length > 0) {
            console.log(`Success! Data found in range: ${range}`);
            console.log("Transactions:");
            res.data.values.forEach((row, index) => {
              console.log(`Row ${index + 1}:`, row);
            });
            dataFound = true;
            break;
          } else {
            console.log(`No data found in range: ${range}`);
          }
        } catch (rangeError) {
          console.log(`Range ${range} failed: ${rangeError.message}`);
        }
      }

      if (!dataFound) {
        console.log("No data found in any of the attempted ranges.");
      }

    } catch (infoError) {
      console.log("Could not get spreadsheet info, trying direct range access...");

      // Fallback: try the original range
      const range = "A1:F10";
      console.log(`Trying fallback range: ${range}`);

      const res = await sheets.spreadsheets.values.get({
        spreadsheetId,
        range,
      });

      if (res.data.values && res.data.values.length > 0) {
        console.log("Transactions found:");
        res.data.values.forEach((row, index) => {
          console.log(`Row ${index + 1}:`, row);
        });
      } else {
        console.log("No data found in the specified range.");
      }
    }
  } catch (error) {
    console.error("Error occurred:", error.message);
    if (error.code) {
      console.error("Error code:", error.code);
    }
  }
}

// Execute the function
listTransactions();
