// dataValidator.js

/**
 * DataValidator class handles data validation rules and formatting for Google Sheets
 */
class DataValidator {
  constructor(sheets, spreadsheetId) {
    this.sheets = sheets;
    this.spreadsheetId = spreadsheetId;
  }

  /**
   * Apply data validation to a range
   * @param {string} sheetName - Name of the sheet
   * @param {string} range - Range to apply validation (e.g., "C2:C1000")
   * @param {Object} validationRule - Validation rule configuration
   * @returns {Promise<void>}
   */
  async applyDataValidation(sheetName, range, validationRule) {
    try {
      // Get sheet ID
      const sheetId = await this.getSheetId(sheetName);
      
      const request = {
        spreadsheetId: this.spreadsheetId,
        resource: {
          requests: [{
            setDataValidation: {
              range: this.convertA1ToGridRange(range, sheetId),
              rule: validationRule
            }
          }]
        }
      };

      await this.sheets.spreadsheets.batchUpdate(request);
      console.log(`✅ Applied data validation to ${sheetName}!${range}`);
    } catch (error) {
      console.error(`❌ Failed to apply data validation to ${sheetName}!${range}:`, error.message);
      throw error;
    }
  }

  /**
   * Create dropdown validation rule from a range
   * @param {string} sourceRange - Source range for dropdown values (e.g., "Categories!A:A")
   * @param {boolean} strict - Whether to reject invalid input
   * @param {string} helpText - Help text for the validation
   * @returns {Object} Validation rule object
   */
  createDropdownValidation(sourceRange, strict = true, helpText = "") {
    return {
      condition: {
        type: 'ONE_OF_RANGE',
        values: [{
          userEnteredValue: sourceRange
        }]
      },
      strict: strict,
      showCustomUi: true,
      inputMessage: helpText
    };
  }

  /**
   * Create list validation rule from array of values
   * @param {Array<string>} values - Array of valid values
   * @param {boolean} strict - Whether to reject invalid input
   * @param {string} helpText - Help text for the validation
   * @returns {Object} Validation rule object
   */
  createListValidation(values, strict = true, helpText = "") {
    return {
      condition: {
        type: 'ONE_OF_LIST',
        values: values.map(value => ({ userEnteredValue: value }))
      },
      strict: strict,
      showCustomUi: true,
      inputMessage: helpText
    };
  }

  /**
   * Create number range validation
   * @param {number} min - Minimum value
   * @param {number} max - Maximum value
   * @param {string} helpText - Help text for the validation
   * @returns {Object} Validation rule object
   */
  createNumberRangeValidation(min, max, helpText = "") {
    return {
      condition: {
        type: 'NUMBER_BETWEEN',
        values: [
          { userEnteredValue: min.toString() },
          { userEnteredValue: max.toString() }
        ]
      },
      strict: true,
      showCustomUi: true,
      inputMessage: helpText
    };
  }

  /**
   * Create date validation
   * @param {string} helpText - Help text for the validation
   * @returns {Object} Validation rule object
   */
  createDateValidation(helpText = "Please enter a valid date") {
    return {
      condition: {
        type: 'DATE_IS_VALID'
      },
      strict: true,
      showCustomUi: true,
      inputMessage: helpText
    };
  }

  /**
   * Apply conditional formatting to a range
   * @param {string} sheetName - Name of the sheet
   * @param {string} range - Range to apply formatting
   * @param {Object} rule - Conditional formatting rule
   * @returns {Promise<void>}
   */
  async applyConditionalFormatting(sheetName, range, rule) {
    try {
      const sheetId = await this.getSheetId(sheetName);
      
      const request = {
        spreadsheetId: this.spreadsheetId,
        resource: {
          requests: [{
            addConditionalFormatRule: {
              rule: {
                ranges: [this.convertA1ToGridRange(range, sheetId)],
                booleanRule: rule
              },
              index: 0
            }
          }]
        }
      };

      await this.sheets.spreadsheets.batchUpdate(request);
      console.log(`✅ Applied conditional formatting to ${sheetName}!${range}`);
    } catch (error) {
      console.error(`❌ Failed to apply conditional formatting to ${sheetName}!${range}:`, error.message);
      throw error;
    }
  }

  /**
   * Apply cell protection to ranges (protect formulas, allow data entry)
   * @param {string} sheetName - Name of the sheet
   * @param {Array<string>} protectedRanges - Ranges to protect
   * @param {Array<string>} editableRanges - Ranges to keep editable
   * @returns {Promise<void>}
   */
  async applyCellProtection(sheetName, protectedRanges, editableRanges = []) {
    try {
      const sheetId = await this.getSheetId(sheetName);
      
      const requests = [];

      // Protect the entire sheet first
      requests.push({
        addProtectedRange: {
          protectedRange: {
            range: {
              sheetId: sheetId
            },
            description: `Protected formulas in ${sheetName}`,
            warningOnly: false
          }
        }
      });

      // Then add exceptions for editable ranges
      editableRanges.forEach(range => {
        requests.push({
          addProtectedRange: {
            protectedRange: {
              range: this.convertA1ToGridRange(range, sheetId),
              description: `Editable data range in ${sheetName}`,
              warningOnly: true,
              editors: {
                users: [] // Allow all users to edit
              }
            }
          }
        });
      });

      if (requests.length > 0) {
        await this.sheets.spreadsheets.batchUpdate({
          spreadsheetId: this.spreadsheetId,
          resource: { requests }
        });
        console.log(`✅ Applied cell protection to ${sheetName}`);
      }
    } catch (error) {
      console.error(`❌ Failed to apply cell protection to ${sheetName}:`, error.message);
      throw error;
    }
  }

  /**
   * Apply standard monthly sheet validations
   * @param {string} sheetName - Name of the monthly sheet
   * @returns {Promise<void>}
   */
  async applyMonthlySheetValidations(sheetName) {
    try {
      console.log(`Applying validations to ${sheetName}...`);

      // Category dropdown validation
      await this.applyDataValidation(
        sheetName,
        "C2:C1000",
        this.createDropdownValidation("Categories!A:A", true, "Select a budget category")
      );

      // Transaction type validation
      await this.applyDataValidation(
        sheetName,
        "E2:E1000",
        this.createListValidation(["Income", "Expense", "Transfer"], true, "Select transaction type")
      );

      // Status validation
      await this.applyDataValidation(
        sheetName,
        "F2:F1000",
        this.createListValidation(["Pending", "Cleared", "Reconciled"], true, "Select transaction status")
      );

      // Date validation
      await this.applyDataValidation(
        sheetName,
        "A2:A1000",
        this.createDateValidation("Enter a valid date (MM/DD/YYYY)")
      );

      // Amount validation (allow negative and positive numbers)
      await this.applyDataValidation(
        sheetName,
        "D2:D1000",
        this.createNumberRangeValidation(-999999, 999999, "Enter a valid amount")
      );

      console.log(`✅ Applied all validations to ${sheetName}`);
    } catch (error) {
      console.error(`❌ Failed to apply validations to ${sheetName}:`, error.message);
      throw error;
    }
  }

  /**
   * Apply conditional formatting for positive/negative amounts
   * @param {string} sheetName - Name of the sheet
   * @param {string} amountRange - Range containing amounts (e.g., "D2:D1000")
   * @returns {Promise<void>}
   */
  async applyAmountConditionalFormatting(sheetName, amountRange) {
    try {
      // Negative amounts in red
      await this.applyConditionalFormatting(sheetName, amountRange, {
        condition: {
          type: 'NUMBER_LESS',
          values: [{ userEnteredValue: '0' }]
        },
        format: {
          textFormat: {
            foregroundColor: { red: 0.8, green: 0.2, blue: 0.2 }
          }
        }
      });

      // Positive amounts in green
      await this.applyConditionalFormatting(sheetName, amountRange, {
        condition: {
          type: 'NUMBER_GREATER',
          values: [{ userEnteredValue: '0' }]
        },
        format: {
          textFormat: {
            foregroundColor: { red: 0.2, green: 0.7, blue: 0.2 }
          }
        }
      });

      console.log(`✅ Applied amount conditional formatting to ${sheetName}!${amountRange}`);
    } catch (error) {
      console.error(`❌ Failed to apply amount conditional formatting:`, error.message);
      throw error;
    }
  }

  /**
   * Helper method to get sheet ID by name
   * @param {string} sheetName - Name of the sheet
   * @returns {Promise<number>} Sheet ID
   */
  async getSheetId(sheetName) {
    const response = await this.sheets.spreadsheets.get({
      spreadsheetId: this.spreadsheetId
    });
    
    const sheet = response.data.sheets.find(s => s.properties.title === sheetName);
    if (!sheet) {
      throw new Error(`Sheet "${sheetName}" not found`);
    }
    
    return sheet.properties.sheetId;
  }

  /**
   * Convert A1 notation to GridRange object
   * @param {string} a1Range - A1 notation range (e.g., "A1:C10")
   * @param {number} sheetId - Sheet ID
   * @returns {Object} GridRange object
   */
  convertA1ToGridRange(a1Range, sheetId) {
    const [start, end] = a1Range.split(':');
    
    const startCol = this.columnToIndex(start.replace(/\d+/g, ''));
    const startRow = parseInt(start.replace(/[A-Z]+/g, '')) - 1;
    
    let endCol, endRow;
    if (end) {
      endCol = this.columnToIndex(end.replace(/\d+/g, '')) + 1;
      endRow = parseInt(end.replace(/[A-Z]+/g, ''));
    } else {
      endCol = startCol + 1;
      endRow = startRow + 1;
    }

    return {
      sheetId: sheetId,
      startRowIndex: startRow,
      endRowIndex: endRow,
      startColumnIndex: startCol,
      endColumnIndex: endCol
    };
  }

  /**
   * Convert column letter to index
   * @param {string} column - Column letter (e.g., "A", "AB")
   * @returns {number} Column index (0-based)
   */
  columnToIndex(column) {
    let result = 0;
    for (let i = 0; i < column.length; i++) {
      result = result * 26 + (column.charCodeAt(i) - 'A'.charCodeAt(0) + 1);
    }
    return result - 1;
  }
}

module.exports = DataValidator;
