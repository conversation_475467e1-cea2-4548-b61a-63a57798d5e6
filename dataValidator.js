// dataValidator.js

/**
 * DataValidator class handles data validation rules and formatting for Google Sheets
 */
class DataValidator {
  constructor(sheets, spreadsheetId) {
    this.sheets = sheets;
    this.spreadsheetId = spreadsheetId;
  }

  /**
   * Apply data validation to a range
   * @param {string} sheetName - Name of the sheet
   * @param {string} range - Range to apply validation (e.g., "C2:C1000")
   * @param {Object} validationRule - Validation rule configuration
   * @returns {Promise<void>}
   */
  async applyDataValidation(sheetName, range, validationRule) {
    try {
      // Get sheet ID
      const sheetId = await this.getSheetId(sheetName);
      
      const request = {
        spreadsheetId: this.spreadsheetId,
        resource: {
          requests: [{
            setDataValidation: {
              range: this.convertA1ToGridRange(range, sheetId),
              rule: validationRule
            }
          }]
        }
      };

      await this.sheets.spreadsheets.batchUpdate(request);
      console.log(`✅ Applied data validation to ${sheetName}!${range}`);
    } catch (error) {
      console.error(`❌ Failed to apply data validation to ${sheetName}!${range}:`, error.message);
      throw error;
    }
  }

  /**
   * Create dropdown validation rule from a range
   * @param {string} sourceRange - Source range for dropdown values (e.g., "Categories!A:A")
   * @param {boolean} strict - Whether to reject invalid input
   * @param {string} helpText - Help text for the validation
   * @returns {Object} Validation rule object
   */
  createDropdownValidation(sourceRange, strict = true, helpText = "") {
    // For cross-sheet references, we need to use a different format
    if (sourceRange.includes('!')) {
      const [sheetName, range] = sourceRange.split('!');
      return {
        condition: {
          type: 'ONE_OF_RANGE',
          values: [{
            userEnteredValue: `='${sheetName}'!${range}`
          }]
        },
        strict: strict,
        showCustomUi: true,
        inputMessage: helpText
      };
    } else {
      return {
        condition: {
          type: 'ONE_OF_RANGE',
          values: [{
            userEnteredValue: sourceRange
          }]
        },
        strict: strict,
        showCustomUi: true,
        inputMessage: helpText
      };
    }
  }

  /**
   * Create list validation rule from array of values
   * @param {Array<string>} values - Array of valid values
   * @param {boolean} strict - Whether to reject invalid input
   * @param {string} helpText - Help text for the validation
   * @returns {Object} Validation rule object
   */
  createListValidation(values, strict = true, helpText = "") {
    return {
      condition: {
        type: 'ONE_OF_LIST',
        values: values.map(value => ({ userEnteredValue: value }))
      },
      strict: strict,
      showCustomUi: true,
      inputMessage: helpText
    };
  }

  /**
   * Create number range validation
   * @param {number} min - Minimum value
   * @param {number} max - Maximum value
   * @param {string} helpText - Help text for the validation
   * @returns {Object} Validation rule object
   */
  createNumberRangeValidation(min, max, helpText = "") {
    return {
      condition: {
        type: 'NUMBER_BETWEEN',
        values: [
          { userEnteredValue: min.toString() },
          { userEnteredValue: max.toString() }
        ]
      },
      strict: true,
      showCustomUi: true,
      inputMessage: helpText
    };
  }

  /**
   * Create date validation
   * @param {string} helpText - Help text for the validation
   * @returns {Object} Validation rule object
   */
  createDateValidation(helpText = "Please enter a valid date") {
    return {
      condition: {
        type: 'DATE_IS_VALID'
      },
      strict: true,
      showCustomUi: true,
      inputMessage: helpText
    };
  }

  /**
   * Apply conditional formatting to a range
   * @param {string} sheetName - Name of the sheet
   * @param {string} range - Range to apply formatting
   * @param {Object} rule - Conditional formatting rule
   * @returns {Promise<void>}
   */
  async applyConditionalFormatting(sheetName, range, rule) {
    try {
      const sheetId = await this.getSheetId(sheetName);
      
      const request = {
        spreadsheetId: this.spreadsheetId,
        resource: {
          requests: [{
            addConditionalFormatRule: {
              rule: {
                ranges: [this.convertA1ToGridRange(range, sheetId)],
                booleanRule: rule
              },
              index: 0
            }
          }]
        }
      };

      await this.sheets.spreadsheets.batchUpdate(request);
      console.log(`✅ Applied conditional formatting to ${sheetName}!${range}`);
    } catch (error) {
      console.error(`❌ Failed to apply conditional formatting to ${sheetName}!${range}:`, error.message);
      throw error;
    }
  }

  /**
   * Apply cell protection to ranges (protect formulas, allow data entry)
   * @param {string} sheetName - Name of the sheet
   * @param {Array<string>} protectedRanges - Ranges to protect
   * @param {Array<string>} editableRanges - Ranges to keep editable
   * @returns {Promise<void>}
   */
  async applyCellProtection(sheetName, protectedRanges, editableRanges = []) {
    try {
      const sheetId = await this.getSheetId(sheetName);
      
      const requests = [];

      // Protect the entire sheet first
      requests.push({
        addProtectedRange: {
          protectedRange: {
            range: {
              sheetId: sheetId
            },
            description: `Protected formulas in ${sheetName}`,
            warningOnly: false
          }
        }
      });

      // Then add exceptions for editable ranges
      editableRanges.forEach(range => {
        requests.push({
          addProtectedRange: {
            protectedRange: {
              range: this.convertA1ToGridRange(range, sheetId),
              description: `Editable data range in ${sheetName}`,
              warningOnly: true,
              editors: {
                users: [] // Allow all users to edit
              }
            }
          }
        });
      });

      if (requests.length > 0) {
        await this.sheets.spreadsheets.batchUpdate({
          spreadsheetId: this.spreadsheetId,
          resource: { requests }
        });
        console.log(`✅ Applied cell protection to ${sheetName}`);
      }
    } catch (error) {
      console.error(`❌ Failed to apply cell protection to ${sheetName}:`, error.message);
      throw error;
    }
  }

  /**
   * Apply standard monthly sheet validations using named ranges
   * @param {string} sheetName - Name of the monthly sheet
   * @returns {Promise<void>}
   */
  async applyMonthlySheetValidations(sheetName) {
    try {
      console.log(`Applying validations to ${sheetName}...`);

      // Category dropdown validation using Categories sheet reference
      await this.applyDataValidation(
        sheetName,
        "C2:C1000",
        this.createDropdownValidation("Categories!A:A", true, "Select a budget category from the list")
      );

      // Transaction type validation using Settings reference
      await this.applyDataValidation(
        sheetName,
        "E2:E1000",
        this.createListValidation(["Income", "Expense", "Transfer"], true, "Select transaction type")
      );

      // Status validation using Settings reference
      await this.applyDataValidation(
        sheetName,
        "F2:F1000",
        this.createListValidation(["Pending", "Cleared", "Reconciled"], true, "Select transaction status")
      );

      // Date validation
      await this.applyDataValidation(
        sheetName,
        "A2:A1000",
        this.createDateValidation("Enter a valid date (MM/DD/YYYY)")
      );

      // Amount validation (allow negative and positive numbers)
      await this.applyDataValidation(
        sheetName,
        "D2:D1000",
        this.createNumberRangeValidation(-999999, 999999, "Enter a valid amount")
      );

      console.log(`✅ Applied all validations to ${sheetName}`);
    } catch (error) {
      console.error(`❌ Failed to apply validations to ${sheetName}:`, error.message);
      throw error;
    }
  }

  /**
   * Apply validations to configuration sheets
   * @returns {Promise<void>}
   */
  async applyConfigurationSheetValidations() {
    try {
      console.log(`Applying validations to configuration sheets...`);

      // Recurring sheet validations
      await this.applyRecurringSheetValidations();

      // Settings sheet conditional formatting
      await this.applySettingsSheetFormatting();

      console.log(`✅ Applied configuration sheet validations`);
    } catch (error) {
      console.error(`❌ Failed to apply configuration validations:`, error.message);
      throw error;
    }
  }

  /**
   * Apply validations to Recurring sheet
   * @returns {Promise<void>}
   */
  async applyRecurringSheetValidations() {
    try {
      // Category dropdown validation
      await this.applyDataValidation(
        'Recurring',
        'C2:C50',
        this.createDropdownValidation('Categories!A:A', true, 'Select category from budget categories')
      );

      // Type validation
      await this.applyDataValidation(
        'Recurring',
        'E2:E50',
        this.createListValidation(['Income', 'Expense', 'Transfer'], true, 'Select transaction type')
      );

      // Frequency validation
      await this.applyDataValidation(
        'Recurring',
        'F2:F50',
        this.createListValidation(['Weekly', 'Bi-Weekly', 'Monthly', 'Quarterly', 'Annual'], true, 'Select frequency')
      );

      // Active status validation (TRUE/FALSE)
      await this.applyDataValidation(
        'Recurring',
        'H2:H50',
        this.createListValidation(['TRUE', 'FALSE'], true, 'Select TRUE or FALSE')
      );

      // Date validation for Next Due Date
      await this.applyDataValidation(
        'Recurring',
        'G2:G50',
        this.createDateValidation('Enter a valid due date')
      );

      console.log(`✅ Applied Recurring sheet validations`);
    } catch (error) {
      console.error(`❌ Failed to apply Recurring sheet validations:`, error.message);
      throw error;
    }
  }

  /**
   * Apply conditional formatting to Settings sheet
   * @returns {Promise<void>}
   */
  async applySettingsSheetFormatting() {
    try {
      // Highlight empty required settings
      await this.applyConditionalFormatting('Settings', 'B2:B10', {
        condition: {
          type: 'BLANK'
        },
        format: {
          backgroundColor: { red: 1, green: 0.8, blue: 0.8 }
        }
      });

      // Highlight validation list headers
      await this.applyConditionalFormatting('Settings', 'A12:A29', {
        condition: {
          type: 'TEXT_CONTAINS',
          values: [{ userEnteredValue: ':' }]
        },
        format: {
          textFormat: { bold: true },
          backgroundColor: { red: 0.9, green: 0.9, blue: 1 }
        }
      });

      console.log(`✅ Applied Settings sheet formatting`);
    } catch (error) {
      console.error(`❌ Failed to apply Settings sheet formatting:`, error.message);
      throw error;
    }
  }

  /**
   * Apply conditional formatting to monthly sheets
   * @param {string} sheetName - Name of the monthly sheet
   * @returns {Promise<void>}
   */
  async applyMonthlySheetFormatting(sheetName) {
    try {
      console.log(`Applying conditional formatting to ${sheetName}...`);

      // Format positive amounts (income) in green
      await this.applyConditionalFormatting(sheetName, 'D2:D500', {
        condition: {
          type: 'NUMBER_GREATER',
          values: [{ userEnteredValue: '0' }]
        },
        format: {
          textFormat: { foregroundColor: { red: 0, green: 0.6, blue: 0 } }
        }
      });

      // Format negative amounts (expenses) in red
      await this.applyConditionalFormatting(sheetName, 'D2:D500', {
        condition: {
          type: 'NUMBER_LESS',
          values: [{ userEnteredValue: '0' }]
        },
        format: {
          textFormat: { foregroundColor: { red: 0.8, green: 0, blue: 0 } }
        }
      });

      // Format overbudget variance in red (negative variance)
      await this.applyConditionalFormatting(sheetName, 'J2:J500', {
        condition: {
          type: 'NUMBER_LESS',
          values: [{ userEnteredValue: '0' }]
        },
        format: {
          backgroundColor: { red: 1, green: 0.8, blue: 0.8 },
          textFormat: { foregroundColor: { red: 0.8, green: 0, blue: 0 } }
        }
      });

      // Format under budget variance in green (positive variance)
      await this.applyConditionalFormatting(sheetName, 'J2:J500', {
        condition: {
          type: 'NUMBER_GREATER',
          values: [{ userEnteredValue: '0' }]
        },
        format: {
          backgroundColor: { red: 0.8, green: 1, blue: 0.8 },
          textFormat: { foregroundColor: { red: 0, green: 0.6, blue: 0 } }
        }
      });

      // Format summary section headers
      await this.applyConditionalFormatting(sheetName, 'A502:A509', {
        condition: {
          type: 'TEXT_CONTAINS',
          values: [{ userEnteredValue: 'SUMMARY' }]
        },
        format: {
          textFormat: { bold: true, fontSize: 12 },
          backgroundColor: { red: 0.9, green: 0.9, blue: 1 }
        }
      });

      console.log(`✅ Applied conditional formatting to ${sheetName}`);
    } catch (error) {
      console.error(`❌ Failed to apply conditional formatting to ${sheetName}:`, error.message);
      throw error;
    }
  }

  /**
   * Apply conditional formatting for positive/negative amounts
   * @param {string} sheetName - Name of the sheet
   * @param {string} amountRange - Range containing amounts (e.g., "D2:D1000")
   * @returns {Promise<void>}
   */
  async applyAmountConditionalFormatting(sheetName, amountRange) {
    try {
      // Negative amounts in red
      await this.applyConditionalFormatting(sheetName, amountRange, {
        condition: {
          type: 'NUMBER_LESS',
          values: [{ userEnteredValue: '0' }]
        },
        format: {
          textFormat: {
            foregroundColor: { red: 0.8, green: 0.2, blue: 0.2 }
          }
        }
      });

      // Positive amounts in green
      await this.applyConditionalFormatting(sheetName, amountRange, {
        condition: {
          type: 'NUMBER_GREATER',
          values: [{ userEnteredValue: '0' }]
        },
        format: {
          textFormat: {
            foregroundColor: { red: 0.2, green: 0.7, blue: 0.2 }
          }
        }
      });

      console.log(`✅ Applied amount conditional formatting to ${sheetName}!${amountRange}`);
    } catch (error) {
      console.error(`❌ Failed to apply amount conditional formatting:`, error.message);
      throw error;
    }
  }

  /**
   * Helper method to get sheet ID by name
   * @param {string} sheetName - Name of the sheet
   * @returns {Promise<number>} Sheet ID
   */
  async getSheetId(sheetName) {
    const response = await this.sheets.spreadsheets.get({
      spreadsheetId: this.spreadsheetId
    });
    
    const sheet = response.data.sheets.find(s => s.properties.title === sheetName);
    if (!sheet) {
      throw new Error(`Sheet "${sheetName}" not found`);
    }
    
    return sheet.properties.sheetId;
  }

  /**
   * Convert A1 notation to GridRange object
   * @param {string} a1Range - A1 notation range (e.g., "A1:C10")
   * @param {number} sheetId - Sheet ID
   * @returns {Object} GridRange object
   */
  convertA1ToGridRange(a1Range, sheetId) {
    const [start, end] = a1Range.split(':');
    
    const startCol = this.columnToIndex(start.replace(/\d+/g, ''));
    const startRow = parseInt(start.replace(/[A-Z]+/g, '')) - 1;
    
    let endCol, endRow;
    if (end) {
      endCol = this.columnToIndex(end.replace(/\d+/g, '')) + 1;
      endRow = parseInt(end.replace(/[A-Z]+/g, ''));
    } else {
      endCol = startCol + 1;
      endRow = startRow + 1;
    }

    return {
      sheetId: sheetId,
      startRowIndex: startRow,
      endRowIndex: endRow,
      startColumnIndex: startCol,
      endColumnIndex: endCol
    };
  }

  /**
   * Convert column letter to index
   * @param {string} column - Column letter (e.g., "A", "AB")
   * @returns {number} Column index (0-based)
   */
  columnToIndex(column) {
    let result = 0;
    for (let i = 0; i < column.length; i++) {
      result = result * 26 + (column.charCodeAt(i) - 'A'.charCodeAt(0) + 1);
    }
    return result - 1;
  }
}

module.exports = DataValidator;
